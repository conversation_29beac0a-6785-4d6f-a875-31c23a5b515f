import java.io.UnsupportedEncodingException;
import java.util.Vector;
import model.MenuItem;
import model.Playlist;

public class Utils {

  private static String playerHttpMethod;

  // reference https://github.com/shinovon/mpgram-client/blob/master/src/MP.java
  public static void setPlayerHttpMethod() {
    String method = Configuration.PLAYER_METHOD_PASS_URL;
    String platform = System.getProperty("microedition.platform");

    boolean symbianJrt = false;
    boolean symbian = false;

    // Platform detection and processing
    if (platform != null) {
      symbianJrt = platform.indexOf("platform=S60") != -1;
      if (symbianJrt) {
        int versionStart = platform.indexOf("platform_version=") + 17;
        int versionEnd = platform.indexOf(';', versionStart);
        String version = platform.substring(versionStart, versionEnd);
      }

      // Handle KEmulator detection
      if (checkClass("emulator.custom.CustomMethod")) {
        platform = "KEmulator";
        String kemulatorVersion = System.getProperty("kemulator.mod.version");
        if (kemulatorVersion != null) {
          platform = platform.concat(" ".concat(kemulatorVersion));
        }
      } else {
        // Clean platform name by removing version info
        int separatorIndex = platform.indexOf('/');
        if (separatorIndex == -1) {
          separatorIndex = platform.indexOf(' ');
        }
        if (separatorIndex != -1) {
          platform = platform.substring(0, separatorIndex);
        }
      }
    }

    // Symbian detection
    symbian =
        symbianJrt
            || hasSymbianProperty("com.symbian.midp.serversocket.support")
            || hasSymbianProperty("com.symbian.default.to.suite.icon")
            || checkClass("com.symbian.midp.io.protocol.http.Protocol")
            || checkClass("com.symbian.lcdjava.io.File");

    // Determine player method
    if (checkClass("com.nokia.mid.impl.isa.jam.Jam")) {
      method =
          checkClass("com.sun.mmedia.protocol.CommonDS")
              ? Configuration.PLAYER_METHOD_PASS_URL
              : Configuration.PLAYER_METHOD_PASS_INPUTSTREAM;
    } else {
      method = Configuration.PLAYER_METHOD_PASS_URL;

      if (symbian) {
        boolean isOldSymbianJrt =
            symbianJrt
                && (platform.indexOf("java_build_version=2.") != -1
                    || platform.indexOf("java_build_version=1.4") != -1);

        if (!isOldSymbianJrt) {
          method = Configuration.PLAYER_METHOD_PASS_INPUTSTREAM;
        }
      } else if (isJ2MELoader()) {
        method = Configuration.PLAYER_METHOD_PASS_INPUTSTREAM;
      }
    }

    playerHttpMethod = method;
  }

  private static boolean hasSymbianProperty(String propertyName) {
    return System.getProperty(propertyName) != null;
  }

  private static boolean isUrlSafeCharacter(char c) {
    return (c >= 'A' && c <= 'Z')
        || (c >= 'a' && c <= 'z')
        || (c >= '0' && c <= '9')
        || c == '-'
        || c == '_'
        || c == '.'
        || c == '~';
  }

  private static boolean checkClass(String s) {
    try {
      Class.forName(s);
      return true;
    } catch (ClassNotFoundException e) {
      return false;
    }
  }

  public static String getDefaultPlayerHttpMethod() {
    setPlayerHttpMethod();
    return playerHttpMethod;
  }

  public static String getPlayerHttpMethod() {
    return playerHttpMethod;
  }

  public static boolean isJ2MELoader() {
    return checkClass("javax.microedition.shell.MicroActivity");
  }

  public static String replace(String text, String searchString, String replacement) {
    StringBuffer sb = new StringBuffer();
    int pos = 0;
    int found;
    int searchLength = searchString.length();
    while ((found = text.indexOf(searchString, pos)) != -1) {
      sb.append(text.substring(pos, found)).append(replacement);
      pos = found + searchLength;
    }
    sb.append(text.substring(pos));
    return sb.toString();
  }

  public static String urlEncode(String text) {
    if (text == null) {
      return "";
    }
    try {
      byte[] rs = text.getBytes("UTF-8");
      final String HEX_DIGITS = "0123456789ABCDEF";
      StringBuffer result = new StringBuffer(rs.length + (rs.length >> 1));

      for (int i = 0; i < rs.length; ++i) {
        int b = rs[i] & 0xFF;
        char c = (char) b;

        if (c == ' ') {
          result.append('+');
        } else if (isUrlSafeCharacter(c)) {
          result.append(c);
        } else {
          result.append('%');
          result.append(HEX_DIGITS.charAt((b >> 4) & 0xF));
          result.append(HEX_DIGITS.charAt(b & 0xF));
        }
      }

      return result.toString();
    } catch (UnsupportedEncodingException e) {
      return "";
    }
  }

  public static void sort(Vector vector, int sortType) {
    for (int i = 0; i < vector.size() - 1; i++) {
      for (int j = 0; j < vector.size() - 1 - i; j++) {
        Object obj1 = vector.elementAt(j);
        Object obj2 = vector.elementAt(j + 1);
        boolean shouldSwap = false;
        if (sortType == 1) {
          Playlist p1 = (Playlist) obj1;
          Playlist p2 = (Playlist) obj2;
          shouldSwap = p1.getId() < p2.getId();
        } else if (sortType == 2) {
          MenuItem m1 = (MenuItem) obj1;
          MenuItem m2 = (MenuItem) obj2;
          shouldSwap = m1.order > m2.order;
        }
        if (shouldSwap) {
          vector.setElementAt(obj2, j);
          vector.setElementAt(obj1, j + 1);
        }
      }
    }
  }

  private Utils() {}
}
