import java.io.UnsupportedEncodingException;
import java.util.Vector;
import javax.microedition.lcdui.Image;
import model.MenuItem;
import model.Playlist;

public class Utils {

  private static String playerHttpMethod;

  // reference https://github.com/shinovon/mpgram-client/blob/master/src/MP.java
  private static void setPlayerHttpMethod() {
    String method = Configuration.PLAYER_METHOD_PASS_URL;
    String platform = System.getProperty("microedition.platform");

    boolean symbianJrt = platform != null && platform.indexOf("platform=S60") != -1;

    boolean symbian =
        symbianJrt
            || hasProperty("com.symbian.midp.serversocket.support")
            || hasProperty("com.symbian.default.to.suite.icon")
            || hasClass("com.symbian.midp.io.protocol.http.Protocol")
            || hasClass("com.symbian.lcdjava.io.File");

    if (hasClass("com.nokia.mid.impl.isa.jam.Jam")) {
      method =
          hasClass("com.sun.mmedia.protocol.CommonDS")
              ? Configuration.PLAYER_METHOD_PASS_URL
              : Configuration.PLAYER_METHOD_PASS_INPUTSTREAM;
    } else {
      method = Configuration.PLAYER_METHOD_PASS_URL;

      if (symbian) {
        boolean isOldSymbianJrt =
            symbianJrt
                && platform != null
                && (platform.indexOf("java_build_version=2.") != -1
                    || platform.indexOf("java_build_version=1.4") != -1);

        if (!isOldSymbianJrt) {
          method = Configuration.PLAYER_METHOD_PASS_INPUTSTREAM;
        }
      } else if (isJ2MELoader()) {
        method = Configuration.PLAYER_METHOD_PASS_INPUTSTREAM;
      }
    }

    playerHttpMethod = method;
  }

  private static boolean isUrlSafeCharacter(char c) {
    return (c >= 'A' && c <= 'Z')
        || (c >= 'a' && c <= 'z')
        || (c >= '0' && c <= '9')
        || c == '-'
        || c == '_'
        || c == '.'
        || c == '~';
  }

  private static boolean hasClass(String s) {
    try {
      Class.forName(s);
      return true;
    } catch (ClassNotFoundException e) {
      return false;
    }
  }

  private static boolean hasProperty(String propertyName) {
    return System.getProperty(propertyName) != null;
  }

  public static String getDefaultPlayerHttpMethod() {
    setPlayerHttpMethod();
    return playerHttpMethod;
  }

  public static String getPlayerHttpMethod() {
    return playerHttpMethod;
  }

  public static boolean isJ2MELoader() {
    return hasClass("javax.microedition.shell.MicroActivity");
  }

  public static String replace(String text, String searchString, String replacement) {
    StringBuffer sb = new StringBuffer();
    int pos = 0;
    int found;
    int searchLength = searchString.length();
    while ((found = text.indexOf(searchString, pos)) != -1) {
      sb.append(text.substring(pos, found)).append(replacement);
      pos = found + searchLength;
    }
    sb.append(text.substring(pos));
    return sb.toString();
  }

  public static String urlEncode(String text) {
    if (text == null) {
      return "";
    }
    try {
      byte[] rs = text.getBytes("UTF-8");
      final String HEX_DIGITS = "0123456789ABCDEF";
      StringBuffer result = new StringBuffer(rs.length + (rs.length >> 1));

      for (int i = 0; i < rs.length; ++i) {
        int b = rs[i] & 0xFF;
        char c = (char) b;

        if (c == ' ') {
          result.append('+');
        } else if (isUrlSafeCharacter(c)) {
          result.append(c);
        } else {
          result.append('%');
          result.append(HEX_DIGITS.charAt((b >> 4) & 0xF));
          result.append(HEX_DIGITS.charAt(b & 0xF));
        }
      }

      return result.toString();
    } catch (UnsupportedEncodingException e) {
      return "";
    }
  }

  public static void sort(Vector vector, int sortType) {
    int size = vector.size();
    for (int i = 0; i < size - 1; i++) {
      for (int j = 0; j < size - 1 - i; j++) {
        Object obj1 = vector.elementAt(j);
        Object obj2 = vector.elementAt(j + 1);

        if (shouldSwapElements(obj1, obj2, sortType)) {
          vector.setElementAt(obj2, j);
          vector.setElementAt(obj1, j + 1);
        }
      }
    }
  }

  private static boolean shouldSwapElements(Object obj1, Object obj2, int sortType) {
    if (sortType == 1) {
      Playlist p1 = (Playlist) obj1;
      Playlist p2 = (Playlist) obj2;
      return p1.getId() < p2.getId();
    } else if (sortType == 2) {
      MenuItem m1 = (MenuItem) obj1;
      MenuItem m2 = (MenuItem) obj2;
      return m1.order > m2.order;
    }
    return false;
  }

  public static Image invertColors(Image img) {
    int width = img.getWidth();
    int height = img.getHeight();
    int[] rgbData = new int[width * height];

    img.getRGB(rgbData, 0, width, 0, 0, width, height);

    for (int i = 0; i < rgbData.length; i++) {
      int argb = rgbData[i];

      int a = (argb >> 24) & 0xFF;
      int r = (argb >> 16) & 0xFF;
      int g = (argb >> 8) & 0xFF;
      int b = argb & 0xFF;

      r = 255 - r;
      g = 255 - g;
      b = 255 - b;

      rgbData[i] = (a << 24) | (r << 16) | (g << 8) | b;
    }

    Image newImg = Image.createRGBImage(rgbData, width, height, true);
    return newImg;
  }

  private Utils() {}
}
